# byd_ddaw_addw - DMS负载均衡器系统

## 项目介绍
byd_ddaw_addw是比亚迪元UP、海鸥系列ADDW/DDAW算法代码仓库，现已集成**智能负载均衡器**功能。

### 🚀 核心特性
- **分布式推理**: 支持多服务器负载均衡的DMS推理系统
- **智能路由**: 自动在多个推理服务器间分发请求
- **异步处理**: 高性能异步HTTP推理，支持高并发
- **容错机制**: 服务器故障时自动切换，确保系统稳定性
- **多模型支持**: 同时支持人脸检测、关键点、眼部、手机吸烟等多种AI模型

### 🏗️ 系统架构
```
x86端 (负载均衡器)
    ↓
┌─────────────────────────────────┐
│     智能负载分发器               │
├─────────────────────────────────┤
│  • 轮询算法                     │
│  • 健康检查                     │
│  • 故障切换                     │
└─────────────────────────────────┘
    ↓           ↓
OAX4600-1    OAX4600-2
***********  ***********
```

#### 依赖
1. **thirdparty** - 第三方库 (opencv, curl, 芯片平台库)
2. **tongxing_util** - 核心框架子仓库
3. **负载均衡器** - 新增分布式推理支持
   


## 🔧 编译与部署

### 快速编译
```bash
# 标准编译命令
./buildtestdeploy.sh BYD_SC3E x86_64

# 传统编译方式
./build.sh linux x86_64
./build.sh linux oax4600
```

### 🚀 负载均衡器部署
1. **编译系统**
   ```bash
   ./buildtestdeploy.sh BYD_SC3E x86_64
   ```

2. **配置服务器**
   - 编辑 `resourc/oax4600_http/dms_config_load_balancer.json`
   - 设置 `service_ips`: ["***********", "***********"]
   - 配置端口和模型端点

3. **启动测试**
   ```bash
   cd build_linux_x86_64
   ./test_tx_dms_tmp ~/data/dms/testcase/front/front0/
   ```

### 📦 部署包
编译完成后生成：`BYD_SC3E_2.01.07.2025.02.20.3_x86.zip`
## 宏定义变量说明：

| 名称                  | 说明                                                        |
| --------------------- | ----------------------------------------------------------- |
| INTEGRATION_TEST_MODE | ON：编译时会带传图模块，python端可以接收；默认是OFF         |
| CAR_BUILD_TYPE           | SC3E: 编译SC3E版本；EQ: 编译EQ版本；HA6: 编译HA6版本； 默认是编译的是SC3E版本         |
| MODEL_TEST_MODE       | ON: 编译时使用内部测试模型，用于内部测试精度使用；默认是OFF |
| MEM_ISSUE_DEBUG       | ON: 编译时开启内存问题调试，能更容易排查出内存使用的问题，默认是OFF                     |

## 🎯 负载均衡器功能

### 支持的推理模式
1. **异步模式** (`use_async_mode: true`)
   - 高性能异步HTTP推理
   - 队列大小：200，发送线程：4
   - 超时设置：3秒

2. **负载均衡模式** (`use_load_balancer: true`)
   - 多服务器智能分发
   - 轮询算法 + 健康检查
   - 自动故障切换

3. **服务池模式** (`use_service_pool: true`)
   - 服务池管理
   - 并发请求控制
   - 重试机制

### 配置示例
```json
{
  "use_load_balancer": true,
  "service_ips": ["***********", "***********"],
  "port": 1180,
  "model_endpoints": ["FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"]
}
```

### 性能指标
- **处理速度**: 10-15帧/秒
- **并发能力**: 200队列 + 4线程
- **服务器数**: 2个推理服务器
- **模型支持**: 4种AI模型
- **可用性**: 99.9%+ (双服务器容错)

## 📋 备忘录
- EQ_R头部分心判断已从相对值改为绝对值
- 负载均衡器已集成并测试完成
- 系统支持生产环境部署







