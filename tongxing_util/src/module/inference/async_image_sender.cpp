#include "async_image_sender.h"
#include <iostream>
#include <algorithm>

namespace tongxing {

AsyncImageSender::AsyncImageSender() 
    : max_queue_size_(100), sender_threads_(1), running_(false), 
      shutdown_requested_(false), processed_count_(0), failed_count_(0), 
      total_send_time_(0.0) {
    curl_global_init(CURL_GLOBAL_DEFAULT);
}

AsyncImageSender::~AsyncImageSender() {
    shutdown();
    curl_global_cleanup();
}

bool AsyncImageSender::initialize(int max_queue_size, int sender_threads) {
    if (running_.load()) {
        std::cerr << "[AsyncImageSender] Already initialized" << std::endl;
        return false;
    }
    
    max_queue_size_ = max_queue_size;
    sender_threads_ = sender_threads;
    running_ = true;
    shutdown_requested_ = false;
    
    // 启动发送线程
    for (int i = 0; i < sender_threads_; ++i) {
        sender_threads_vec_.emplace_back(&AsyncImageSender::senderThreadFunc, this);
    }
    
    std::cout << "[AsyncImageSender] Initialized with " << sender_threads_ 
              << " sender threads, max queue size: " << max_queue_size_ << std::endl;
    
    return true;
}

void AsyncImageSender::shutdown() {
    if (!running_.load()) {
        return;
    }
    
    std::cout << "[AsyncImageSender] Shutting down..." << std::endl;
    
    shutdown_requested_ = true;
    queue_cv_.notify_all();
    
    // 等待所有线程结束
    for (auto& thread : sender_threads_vec_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    sender_threads_vec_.clear();
    running_ = false;
    
    // 清空队列
    std::lock_guard<std::mutex> lock(queue_mutex_);
    while (!request_queue_.empty()) {
        request_queue_.pop();
    }
    
    std::cout << "[AsyncImageSender] Shutdown completed. Processed: " 
              << processed_count_.load() << ", Failed: " << failed_count_.load() << std::endl;
}

bool AsyncImageSender::sendImageAsync(const std::vector<std::shared_ptr<NumArray>>& inputs,
                                     const std::string& base_url) {
    if (!running_.load()) {
        std::cerr << "[AsyncImageSender] Not initialized" << std::endl;
        return false;
    }
    
    // 转换输入数据
    auto image_data = convertInputsToImageData(inputs);
    if (image_data.empty()) {
        std::cerr << "[AsyncImageSender] Failed to convert input data" << std::endl;
        return false;
    }
    
    // 创建请求
    ImageRequest request(image_data, base_url);
    
    // 非阻塞入队
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        // 如果队列满了，等待一小段时间或直接丢弃
        if (request_queue_.size() >= static_cast<size_t>(max_queue_size_)) {
            // 等待100ms，如果还是满的就丢弃
            if (!queue_not_full_cv_.wait_for(lock, std::chrono::milliseconds(100),
                [this] { return request_queue_.size() < static_cast<size_t>(max_queue_size_) || shutdown_requested_.load(); })) {
                std::cerr << "[AsyncImageSender] Queue full, dropping request" << std::endl;
                return false;
            }
        }
        
        if (shutdown_requested_.load()) {
            return false;
        }
        
        request_queue_.push(std::move(request));
    }
    
    queue_cv_.notify_one();
    return true;
}

bool AsyncImageSender::sendImageAsyncWithCallback(
    const std::vector<std::shared_ptr<NumArray>>& inputs,
    const std::string& base_url,
    std::function<void(bool, const std::string&)> callback) {
    
    if (!running_.load()) {
        std::cerr << "[AsyncImageSender] Not initialized" << std::endl;
        return false;
    }
    
    auto image_data = convertInputsToImageData(inputs);
    if (image_data.empty()) {
        std::cerr << "[AsyncImageSender] Failed to convert input data" << std::endl;
        return false;
    }
    
    ImageRequest request(image_data, base_url);
    request.callback = callback;
    
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        if (request_queue_.size() >= static_cast<size_t>(max_queue_size_)) {
            if (!queue_not_full_cv_.wait_for(lock, std::chrono::milliseconds(100),
                [this] { return request_queue_.size() < static_cast<size_t>(max_queue_size_) || shutdown_requested_.load(); })) {
                std::cerr << "[AsyncImageSender] Queue full, dropping request with callback" << std::endl;
                return false;
            }
        }
        
        if (shutdown_requested_.load()) {
            return false;
        }
        
        request_queue_.push(std::move(request));
    }
    
    queue_cv_.notify_one();
    return true;
}

void AsyncImageSender::senderThreadFunc() {
    std::cout << "[AsyncImageSender] Sender thread started" << std::endl;
    
    while (running_.load()) {
        ImageRequest request(std::vector<unsigned char>(), "", "");
        bool has_request = false;
        
        // 获取请求
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] { 
                return !request_queue_.empty() || shutdown_requested_.load(); 
            });
            
            if (shutdown_requested_.load() && request_queue_.empty()) {
                break;
            }
            
            if (!request_queue_.empty()) {
                request = std::move(request_queue_.front());
                request_queue_.pop();
                has_request = true;
                queue_not_full_cv_.notify_one();
            }
        }
        
        // 处理请求
        if (has_request) {
            auto start_time = std::chrono::high_resolution_clock::now();
            bool success = executeRequest(request);
            auto end_time = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration<double, std::milli>(end_time - start_time).count();
            total_send_time_.store(total_send_time_.load() + duration);
            
            if (success) {
                processed_count_.fetch_add(1);
            } else {
                failed_count_.fetch_add(1);
            }
        }
    }
    
    std::cout << "[AsyncImageSender] Sender thread stopped" << std::endl;
}

bool AsyncImageSender::executeRequest(const ImageRequest& request) {
    CURL* curl = curl_easy_init();
    if (!curl) {
        std::cerr << "[AsyncImageSender] Failed to initialize CURL" << std::endl;
        if (request.callback) {
            request.callback(false, "Failed to initialize CURL");
        }
        return false;
    }

    std::string response_data;
    bool success = false;

    try {
        struct curl_slist* headers = nullptr;
        std::string content_type_header = "Content-Type: " + request.content_type;
        headers = curl_slist_append(headers, content_type_header.c_str());

        // 打印正在使用的具体URL
        std::cout << "[AsyncImageSender] Sending request to URL: " << request.url << std::endl;

        curl_easy_setopt(curl, CURLOPT_URL, request.url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS,
                        reinterpret_cast<const char*>(request.image_data.data()));
        curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, request.image_data.size());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, 3000); // 优化：3秒超时，提高响应速度
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_data);

        CURLcode res = curl_easy_perform(curl);

        if (res == CURLE_OK) {
            long http_code = 0;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
            success = (http_code == 200);

            if (!success) {
                std::cerr << "[AsyncImageSender] HTTP error: " << http_code << std::endl;
            }
        } else {
            std::cerr << "[AsyncImageSender] CURL error: " << curl_easy_strerror(res) << std::endl;
        }

        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);

    } catch (const std::exception& e) {
        std::cerr << "[AsyncImageSender] Exception: " << e.what() << std::endl;
        curl_easy_cleanup(curl);
    }

    // 调用回调函数（如果有）
    if (request.callback) {
        request.callback(success, response_data);
    }

    return success;
}

std::vector<unsigned char> AsyncImageSender::convertInputsToImageData(
    const std::vector<std::shared_ptr<NumArray>>& inputs) {

    if (inputs.empty() || !inputs[0]) {
        return {};
    }

    auto input = inputs[0];
    auto batch_size = input->shape[0];
    auto channel = input->shape[1];
    auto height = input->shape[2];
    auto width = input->shape[3];

    int data_length = batch_size * channel * height * width;

    std::vector<unsigned char> image_data;
    image_data.reserve(data_length);
    image_data.assign(input->data, input->data + data_length);

    return image_data;
}

size_t AsyncImageSender::getQueueSize() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return request_queue_.size();
}

size_t AsyncImageSender::getProcessedCount() const {
    return processed_count_.load();
}

size_t AsyncImageSender::getFailedCount() const {
    return failed_count_.load();
}

double AsyncImageSender::getAverageSendTime() const {
    size_t total_requests = processed_count_.load() + failed_count_.load();
    if (total_requests == 0) {
        return 0.0;
    }
    return total_send_time_.load() / total_requests;
}

size_t AsyncImageSender::WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

} // namespace tongxing
