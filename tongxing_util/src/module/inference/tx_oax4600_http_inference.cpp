#include "tx_oax4600_http_inference.h"
#include <google/protobuf/message.h>
#include <stdio.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <iostream>
#include <cstring>  // 添加memset支持
#include "cc_resource_register.h"
#include "inference_result.pb.h"  // Protobuf 生成的头文件
#include "CalmCarLog.h"  // 添加日志支持

namespace tongxing {

int TXOAX4600HttpInference::init(const Json::Value& root) {
    int ret = 0;
    root_ = root;

    if (root_["type"].asString() == "inside") {
        http_url_ = root_["http_url"].asString();  //通过http方式指定模型推理url
        model_url_ = root_["model_url"].asString();

    } else {
        cc_assert(false);
    }

    // 检查模式配置
    use_pool_mode_ = root_.get("use_service_pool", false).asBool();
    use_async_mode_ = root_.get("use_async_mode", true).asBool(); // 默认启用异步模式

    // 优先尝试异步模式
    if (use_async_mode_) {
        async_sender_ = std::make_shared<AsyncImageSender>();
        if (async_sender_->initialize(200, 4)) { // 优化：队列大小200，4个发送线程提高并发
            std::cout << "[TXOAX4600HttpInference] Async sender initialized successfully" << std::endl;
            uploader.reset(new FileUploader(async_sender_, http_url_));
            return ret;
        } else {
            std::cout << "[TXOAX4600HttpInference] Async sender initialization failed, trying service pool mode" << std::endl;
            use_async_mode_ = false;
        }
    }

    // 尝试服务池模式
    if (use_pool_mode_) {
        service_pool_ = std::make_shared<ModelServicePool>();
        if (service_pool_->initialize("ip_pool.json")) {
            std::cout << "[TXOAX4600HttpInference] Service pool initialized successfully" << std::endl;
            uploader.reset(new FileUploader(service_pool_));
            return ret;
        } else {
            std::cout << "[TXOAX4600HttpInference] Service pool initialization failed, falling back to legacy mode" << std::endl;
            use_pool_mode_ = false;
        }
    }

    //检测执行程序下是否存在存在指定IP+port文件（传统模式）
    std::string config_file = "ip_port.json";
    if (access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader;
        Json::Value root;
        std::ifstream infile(config_file, std::ios::binary);
        json_reader.parse(infile, root);

        std::string ip = root["ip"].asString();
        std::string port = root["port"].asString();

        // 查找最后一个斜杠的位置
        size_t lastSlashPos = http_url_.find_last_of('/');
        size_t lastSlashPos2 = model_url_.find_last_of('/');
        auto temp_http_url = http_url_;
        auto temp_model_url = model_url_;

        http_url_ = "http://" + ip + ":" + port + temp_http_url.substr(lastSlashPos);

        model_url_ = "http://" + ip + ":" + port + temp_model_url.substr(lastSlashPos2);
    }
    uploader.reset(new FileUploader(http_url_, model_url_));

    return ret;
}

TXOAX4600HttpInference::~TXOAX4600HttpInference() {}
int TXOAX4600HttpInference::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}
int TXOAX4600HttpInference::execute() {
    std::vector<std::shared_ptr<NumArray>> outputs;

    // 检查是否为异步模式
    if (use_async_mode_ && async_sender_) {
        // 异步模式：发送请求但不等待结果
        uploader->upload_image_data(input_, outputs);

        // 为异步模式创建占位符输出，避免getOutput时访问空向量
        // 这些占位符将在实际应用中被忽略或由回调处理
        TX_LOG_INFO("TXOAX4600HttpInference", "Async mode: request sent, creating placeholder outputs");

        // 创建合适大小的占位符输出（根据DMS推理的实际输出格式）
        // 这样可以避免崩溃，但实际数据需要通过异步回调获取

        // 为人脸检测创建占位符：根据配置 feat_width=20, feat_height=12, 格式为 [batch, feat_w*feat_h, 5]
        auto face_detection_placeholder = creat_numarray({1, 240, 5}, NumArray::DataType::FLOAT32);
        // 初始化为零，确保不会触发检测逻辑
        memset(face_detection_placeholder->data, 0, 1 * 240 * 5 * sizeof(float));
        outputs.push_back(face_detection_placeholder);

        // 为关键点检测创建占位符：通常是 [batch, 240, 10] 的格式
        auto keypoints_placeholder = creat_numarray({1, 240, 10}, NumArray::DataType::FLOAT32);
        memset(keypoints_placeholder->data, 0, 1 * 240 * 10 * sizeof(float));
        outputs.push_back(keypoints_placeholder);

        // 为眼部检测创建占位符：通常是 [batch, 240, 2] 的格式
        auto eye_placeholder = creat_numarray({1, 240, 2}, NumArray::DataType::FLOAT32);
        memset(eye_placeholder->data, 0, 1 * 240 * 2 * sizeof(float));
        outputs.push_back(eye_placeholder);

        // 为手机/吸烟检测创建占位符：通常是 [batch, 240, 3] 的格式
        auto phone_smoking_placeholder = creat_numarray({1, 240, 3}, NumArray::DataType::FLOAT32);
        memset(phone_smoking_placeholder->data, 0, 1 * 240 * 3 * sizeof(float));
        outputs.push_back(phone_smoking_placeholder);

    } else {
        // 同步模式：正常处理
        uploader->upload_image_data(input_, outputs);
    }

    output_ = outputs;
    return 0;
}
size_t TXOAX4600HttpInference::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> TXOAX4600HttpInference::getOutput(int index) {
    // 添加边界检查，防止访问空向量或越界
    if (output_.empty()) {
        TX_LOG_INFO("TXOAX4600HttpInference", "Warning: output vector is empty, returning nullptr");
        return nullptr;
    }

    if (index < 0 || index >= static_cast<int>(output_.size())) {
        TX_LOG_INFO("TXOAX4600HttpInference", "Warning: index %d out of range [0, %zu), returning nullptr",
                   index, output_.size());
        return nullptr;
    }

    return output_[index];
}
REGISTER_CC_MODULE(oax4600_http_inference, TXOAX4600HttpInference)
}  // namespace tongxing