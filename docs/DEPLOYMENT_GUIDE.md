# DMS负载均衡器部署指南

## 🎯 概述

本指南详细说明如何部署和配置DMS负载均衡器系统，实现多服务器分布式推理。

## 🏗️ 系统架构

```
┌─────────────────┐    HTTP请求    ┌─────────────────┐
│   x86端主机     │ ──────────────→ │  OAX4600服务器1  │
│  (负载均衡器)    │                │  ***********    │
│                 │ ──────────────→ │                 │
└─────────────────┘    负载分发    └─────────────────┘
                                   ┌─────────────────┐
                                   │  OAX4600服务器2  │
                                   │  ***********    │
                                   └─────────────────┘
```

## 📋 部署前准备

### 1. 硬件要求
- **x86主机**: 4核CPU, 8GB内存, 网络连接
- **OAX4600服务器**: 至少2台，网络可达
- **网络**: 千兆以太网，延迟<10ms

### 2. 软件依赖
- Linux x86_64系统
- GCC 9.4+
- CMake 3.16+
- CURL库
- OpenCV库

## 🔧 编译部署

### 1. 编译系统
```bash
# 克隆代码仓库
git clone <repository_url>
cd byd_ddaw_addw

# 编译生成部署包
./buildtestdeploy.sh BYD_SC3E x86_64

# 检查生成的部署包
ls build_linux_x86_64/BYD_SC3E_*.zip
```

### 2. 部署包内容
```
BYD_SC3E_2.01.07.2025.02.20.3_x86.zip
├── libtx_dms.so                    # 核心库文件
├── model/                          # 模型文件目录
│   ├── FaceDetection.ovm
│   ├── FaceKeypoints.ovm
│   ├── eye.ovm
│   └── Dms_PhoneSmoking.ovm
└── test_tongxing_dms_oax4600_http_image_tool  # 测试工具
```

## ⚙️ 配置说明

### 1. 负载均衡器配置
编辑 `resourc/oax4600_http/dms_config_load_balancer.json`:

```json
{
    "service_ips": ["***********", "***********"],
    "port": 1180,
    "model_endpoints": [
        "FaceDetection",
        "FaceKeypoints", 
        "eye",
        "Dms_PhoneSmoking"
    ],
    "max_concurrent_requests": 10,
    "request_timeout_ms": 3000,
    "retry_attempts": 2,
    "health_check_interval_ms": 5000,
    "load_balance_strategy": "round_robin"
}
```

### 2. DMS主配置
编辑 `resourc/oax4600_http/dms_config.json`:

```json
{
    "models": [
        {
            "class_name": "oax4600_http_inference",
            "config": {
                "type": "inside",
                "use_load_balancer": true,
                "service_ips": ["***********", "***********"],
                "port": 1180,
                "model_endpoints": ["FaceDetection"],
                "http_url": "http://***********:1180/FaceDetection",
                "model_url": "http://***********:1180/tar"
            },
            "name": "faceDet_infer"
        }
    ]
}
```

### 3. 异步模式配置
```json
{
    "use_async_mode": true,
    "use_load_balancer": false,
    "queue_size": 200,
    "sender_threads": 4,
    "timeout_ms": 3000
}
```

## 🚀 启动与测试

### 1. 启动系统
```bash
cd build_linux_x86_64

# 基本功能测试
./test_tx_dms_tmp ~/data/dms/testcase/front/front0/

# 负载均衡器测试
./test_load_balancer_integration

# URL显示测试
./test_url_display
```

### 2. 验证负载均衡
观察日志输出，确认请求在多个服务器间分发：
```
[AsyncImageSender] Sending request to URL: http://***********:1180/FaceDetection
[AsyncImageSender] Sending request to URL: http://***********:1180/FaceKeypoints
[SingleServiceMultiModelProcessor] Calling endpoint: http://***********:1180/eye
[SingleServiceMultiModelProcessor] Calling endpoint: http://***********:1180/Dms_PhoneSmoking
```

## 📊 监控与维护

### 1. 性能监控
- **处理速度**: 目标10-15帧/秒
- **队列状态**: 监控队列大小和处理延迟
- **服务器健康**: 检查各服务器响应时间

### 2. 故障处理
- **服务器离线**: 系统自动切换到可用服务器
- **网络超时**: 3秒超时后重试
- **队列满载**: 丢弃新请求，记录日志

### 3. 日志分析
```bash
# 查看处理统计
grep "Processed:" logs/dms.log

# 查看错误信息  
grep "CURL error\|Queue full" logs/dms.log

# 查看URL分发
grep "Sending request to URL" logs/dms.log
```

## 🔧 故障排除

### 常见问题

1. **编译失败**
   - 检查GCC版本和依赖库
   - 确认CMake配置正确

2. **网络连接失败**
   - 验证服务器IP和端口
   - 检查防火墙设置

3. **性能不佳**
   - 调整队列大小和线程数
   - 优化网络延迟

4. **负载不均衡**
   - 检查服务器健康状态
   - 验证负载均衡算法配置

## 📈 性能优化

### 1. 参数调优
- `queue_size`: 根据内存情况调整(100-500)
- `sender_threads`: 根据CPU核数调整(2-8)
- `timeout_ms`: 根据网络情况调整(1000-5000)

### 2. 网络优化
- 使用千兆网络
- 减少网络跳数
- 启用TCP优化

### 3. 系统优化
- 调整系统TCP参数
- 优化内存分配
- 使用SSD存储

## 📞 技术支持

如遇问题，请提供：
1. 系统配置信息
2. 错误日志
3. 网络拓扑
4. 性能数据

---

**部署完成后，您将拥有一个高性能、高可用的DMS分布式推理系统！**
