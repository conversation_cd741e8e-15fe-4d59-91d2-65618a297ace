#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include "tongxing_util/src/module/inference/async_image_sender.h"
#include "tongxing_util/src/util/cc_numarray.h"

using namespace tongxing;

int main() {
    std::cout << "=== URL Display Test ===" << std::endl;
    
    // 创建异步发送器
    AsyncImageSender sender;
    if (!sender.initialize(10, 1)) {
        std::cerr << "Failed to initialize async sender" << std::endl;
        return -1;
    }
    
    // 创建测试数据
    auto test_data = creat_numarray({1, 3, 224, 224}, NumArray::DataType::UINT8);
    memset(test_data->data, 128, 1 * 3 * 224 * 224);
    
    std::vector<std::shared_ptr<NumArray>> inputs;
    inputs.push_back(test_data);
    
    // 测试不同的URL
    std::vector<std::string> test_urls = {
        "http://192.168.7.1:1180/FaceDetection",
        "http://192.168.8.1:1180/FaceDetection", 
        "http://192.168.7.1:1180/FaceKeypoints",
        "http://192.168.8.1:1180/FaceKeypoints"
    };
    
    std::cout << "Testing URL usage with AsyncImageSender:" << std::endl;
    
    for (int i = 0; i < test_urls.size(); i++) {
        std::cout << "Test " << (i+1) << ": Sending to " << test_urls[i] << std::endl;
        
        bool success = sender.sendImageAsync(inputs, test_urls[i]);
        if (success) {
            std::cout << "  -> Request queued successfully" << std::endl;
        } else {
            std::cout << "  -> Request failed to queue" << std::endl;
        }
        
        // 等待一下让请求处理
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 等待处理完成
    std::cout << "Waiting for requests to complete..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    std::cout << "Processed: " << sender.getProcessedCount() << std::endl;
    std::cout << "Failed: " << sender.getFailedCount() << std::endl;
    std::cout << "Queue size: " << sender.getQueueSize() << std::endl;
    
    sender.shutdown();
    
    std::cout << "=== Test Complete ===" << std::endl;
    return 0;
}
